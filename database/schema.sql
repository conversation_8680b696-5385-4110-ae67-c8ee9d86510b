-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
-- Job Postings Table
CREATE TABLE job_postings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    company TEXT NOT NULL,
    location TEXT NOT NULL,
    salary_min DOUBLE PRECISION,
    salary_max DOUBLE PRECISION,
    currency TEXT,
    description TEXT NOT NULL,
    requirements TEXT [] NOT NULL,
    platform TEXT NOT NULL,
    external_id TEXT NOT NULL,
    url TEXT NOT NULL,
    posted_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
-- Add indexes for job postings
CREATE INDEX idx_job_postings_title ON job_postings USING gin (to_tsvector('english', title));
CREATE INDEX idx_job_postings_company ON job_postings (company);
CREATE INDEX idx_job_postings_location ON job_postings (location);
CREATE INDEX idx_job_postings_platform ON job_postings (platform);
CREATE INDEX idx_job_postings_external_id ON job_postings (external_id);
CREATE INDEX idx_job_postings_salary ON job_postings (salary_min, salary_max);
CREATE INDEX idx_job_postings_posted_at ON job_postings (posted_at DESC);
-- Add unique constraint for platform + external_id
CREATE UNIQUE INDEX idx_job_postings_platform_external_id ON job_postings (platform, external_id);
-- User Preferences Table
CREATE TABLE user_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    keywords TEXT [] NOT NULL DEFAULT '{}',
    locations TEXT [] NOT NULL DEFAULT '{}',
    salary_range NUMRANGE,
    remote_only BOOLEAN NOT NULL DEFAULT false,
    experience_level TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
-- Add indexes for user preferences
CREATE INDEX idx_user_preferences_user_id ON user_preferences (user_id);
-- Add unique constraint for user_id (one preference set per user)
CREATE UNIQUE INDEX idx_user_preferences_user_id_unique ON user_preferences (user_id);
-- Function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column() RETURNS TRIGGER AS $$ BEGIN NEW.updated_at = now();
RETURN NEW;
END;
$$ language 'plpgsql';
-- Add triggers for updated_at
CREATE TRIGGER update_job_postings_updated_at BEFORE
UPDATE ON job_postings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_preferences_updated_at BEFORE
UPDATE ON user_preferences FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
-- Add RLS (Row Level Security) policies
ALTER TABLE job_postings ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;
-- Job postings are readable by all, but only modifiable by authenticated users
CREATE POLICY job_postings_read_policy ON job_postings FOR
SELECT USING (true);
CREATE POLICY job_postings_modify_policy ON job_postings FOR ALL USING (auth.role() = 'authenticated');
-- User preferences are only accessible by their owners
CREATE POLICY user_preferences_policy ON user_preferences FOR ALL USING (auth.uid() = user_id);
-- Comments for documentation
COMMENT ON TABLE job_postings IS 'Stores job postings collected from various platforms';
COMMENT ON TABLE user_preferences IS 'Stores user search preferences and filters';
COMMENT ON COLUMN job_postings.requirements IS 'Array of job requirements';
COMMENT ON COLUMN job_postings.platform IS 'Source platform (e.g., LinkedIn, Djinni)';
COMMENT ON COLUMN job_postings.external_id IS 'Original ID from the source platform';
COMMENT ON COLUMN user_preferences.keywords IS 'Array of search keywords';
COMMENT ON COLUMN user_preferences.locations IS 'Array of preferred locations';
COMMENT ON COLUMN user_preferences.salary_range IS 'Preferred salary range';
COMMENT ON COLUMN user_preferences.experience_level IS 'Preferred experience level';