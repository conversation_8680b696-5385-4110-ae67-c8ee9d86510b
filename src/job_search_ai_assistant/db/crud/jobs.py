"""Job posting repository for database operations."""

from datetime import datetime
from typing import <PERSON><PERSON>
from uuid import UUID

from supabase import Client

from src.job_search_ai_assistant.api.schemas.database import JobPosting, JobPostingCreate


class JobRepository:
    """Repository for job posting operations."""

    def __init__(self, db: Client):
        """Initialize repository with database client."""
        self.db = db
        self.table = "job_postings"

    async def create(self, job: JobPostingCreate) -> JobPosting:
        """Create a new job posting."""
        data = job.model_dump()
        result = self.db.table(self.table).insert(data).execute()
        return JobPosting(**result.data[0])

    async def get(self, job_id: UUID) -> Optional[JobPosting]:
        """Get a job posting by ID."""
        result = self.db.table(self.table).select("*").eq("id", str(job_id)).execute()
        return JobPosting(**result.data[0]) if result.data else None

    async def list(self, limit: int = 100, offset: int = 0) -> list[JobPosting]:
        """List job postings with pagination."""
        result = (
            self.db.table(self.table)
            .select("*")
            .order("created_at", desc=True)
            .range(offset, offset + limit - 1)
            .execute()
        )
        return [JobPosting(**item) for item in result.data]

    async def update(self, job_id: UUID, job_data: dict) -> Optional[JobPosting]:
        """Update a job posting."""
        job_data["updated_at"] = datetime.utcnow()
        result = self.db.table(self.table).update(job_data).eq("id", str(job_id)).execute()
        return JobPosting(**result.data[0]) if result.data else None

    async def delete(self, job_id: UUID) -> bool:
        """Delete a job posting."""
        result = self.db.table(self.table).delete().eq("id", str(job_id)).execute()
        return bool(result.data)

    async def search(
        self,
        keyword: Optional[str] = None,
        location: Optional[str] = None,
        min_salary: Optional[float] = None,
        max_salary: Optional[float] = None,
        platform: Optional[str] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[JobPosting]:
        """Search job postings with filters."""
        query = self.db.table(self.table).select("*")

        if keyword:
            query = query.or_(f"title.ilike.%{keyword}%,description.ilike.%{keyword}%")

        if location:
            query = query.ilike("location", f"%{location}%")

        if min_salary is not None:
            query = query.gte("salary_min", min_salary)

        if max_salary is not None:
            query = query.lte("salary_max", max_salary)

        if platform:
            query = query.eq("platform", platform)

        result = query.order("created_at", desc=True).range(offset, offset + limit - 1).execute()
        return [JobPosting(**item) for item in result.data]
