"""User preferences repository for database operations."""

from datetime import datetime
from typing import Optional
from uuid import UUID

from supabase import Client

from src.job_search_ai_assistant.api.schemas.database import UserPreference, UserPreferenceCreate


class PreferenceRepository:
    """Repository for user preferences operations."""

    def __init__(self, db: Client):
        """Initialize repository with database client."""
        self.db = db
        self.table = "user_preferences"

    async def create(self, preference: UserPreferenceCreate) -> UserPreference:
        """Create a new user preference."""
        data = preference.model_dump()
        result = self.db.table(self.table).insert(data).execute()
        return UserPreference(**result.data[0])

    async def get(self, preference_id: UUID) -> Optional[UserPreference]:
        """Get a user preference by ID."""
        result = self.db.table(self.table).select("*").eq("id", str(preference_id)).execute()
        return UserPreference(**result.data[0]) if result.data else None

    async def get_by_user(self, user_id: UUID) -> Optional[UserPreference]:
        """Get preferences for a specific user."""
        result = self.db.table(self.table).select("*").eq("user_id", str(user_id)).execute()
        return UserPreference(**result.data[0]) if result.data else None

    async def update(self, preference_id: UUID, data: dict) -> Optional[UserPreference]:
        """Update user preferences."""
        data["updated_at"] = datetime.utcnow()
        result = self.db.table(self.table).update(data).eq("id", str(preference_id)).execute()
        return UserPreference(**result.data[0]) if result.data else None

    async def delete(self, preference_id: UUID) -> bool:
        """Delete user preferences."""
        result = self.db.table(self.table).delete().eq("id", str(preference_id)).execute()
        return bool(result.data)

    async def list(self, limit: int = 100, offset: int = 0) -> list[UserPreference]:
        """List all user preferences with pagination."""
        result = (
            self.db.table(self.table)
            .select("*")
            .order("created_at", desc=True)
            .range(offset, offset + limit - 1)
            .execute()
        )
        return [UserPreference(**item) for item in result.data]
