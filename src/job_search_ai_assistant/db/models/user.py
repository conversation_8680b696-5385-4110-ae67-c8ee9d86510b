from datetime import datetime
from uuid import UUID, uuid4

from sqlmodel import Field, SQLModel


class User(SQLModel, table=True):
    __tablename__ = "user"

    id: UUID = Field(
        default_factory=uuid4, primary_key=True, foreign_key="auth.users.id"
    )
    email: str = Field(
        index=True, unique=True, max_length=255, description="User's email address"
    )
    full_name: str = Field(
        default=None, max_length=255, description="User's full name"
    )
    profile_picture: str = Field(
        default=None, max_length=255, description="URL to user's profile picture"
    )
    password_hash: str = Field(
        default=None, max_length=255, description="Hashed password for the user"
    )
    last_login: datetime = Field(
        default=None, description="Timestamp of the user's last login"
    )
    last_activity: datetime = Field(
        default_factory=datetime.now, description="Timestamp of the user's last activity"
    )
    is_verified: bool = Field(
        default=False, description="Indicates if the user's email is verified"
    )

    is_active: bool = True
    created_at: datetime = Field(default_factory=datetime.now)
