"""Database models and schemas."""

from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, Field, HttpUrl


class JobPostingBase(BaseModel):
    """Base model for job posting data."""

    title: str
    company: str
    location: str
    salary_min: Optional[float] = None
    salary_max: Optional[float] = None
    currency: Optional[str] = None
    description: str
    requirements: list[str]
    platform: str
    external_id: str
    url: HttpUrl
    posted_at: datetime

    class Config:
        """Model configuration."""

        from_attributes = True


class JobPostingCreate(JobPostingBase):
    """Schema for creating a new job posting."""

    pass


class JobPosting(JobPostingBase):
    """Schema for job posting with database fields."""

    id: UUID
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class UserPreferenceBase(BaseModel):
    """Base model for user preferences."""

    keywords: list[str]
    locations: list[str]
    salary_range: Optional[tuple[float, float]] = None
    remote_only: bool = False
    experience_level: Optional[str] = None

    class Config:
        """Model configuration."""

        from_attributes = True


class UserPreferenceCreate(UserPreferenceBase):
    """Schema for creating user preferences."""

    user_id: UUID


class UserPreference(UserPreferenceBase):
    """Schema for user preferences with database fields."""

    id: UUID
    user_id: UUID
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
