import os
from typing import Annotated

from dotenv import load_dotenv
from fastapi import Depends, HTTPException
from supabase import Client, create_client

from ..db.models.user import User
from .config import logger

load_dotenv()

# Supabase connection
url = os.environ.get("SUPABASE_URL")
key = os.environ.get("SUPABASE_KEY")


def get_supabase_client() -> Client:
    logger.info("Initializing Supabase client")
    return create_client(url, key)


SupabaseDependency = Annotated[Client, Depends(get_supabase_client)]


async def get_current_user(
    supabase_client: SupabaseDependency,
    jwt: str | None = None,
) -> User:
    """Get the current user from the access token or from the current session."""
    try:
        user = supabase_client.auth.get_user(jwt=jwt)
    except Exception as e:
        logger.error("Error during getting user %s", e)
        raise HTTPException(status_code=401, detail="Invalid token") from e

    return user


UserDependency = Annotated[User, Depends(get_current_user)]
